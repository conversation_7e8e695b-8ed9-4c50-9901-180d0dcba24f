package com.wexl.admissiontests.repository;

import com.wexl.admissiontests.dto.AdmissionTestMetricResult;
import com.wexl.admissiontests.model.AdmissionTests;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AdmissionTestRepository extends JpaRepository<AdmissionTests, Long> {
  AdmissionTests findByTestScheduleId(Long testScheduleId);

  List<AdmissionTests> findAllByOrgSlugOrderByCreatedAtDesc(String orgSlug);

  List<AdmissionTests> findAllByCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
      Timestamp fromDate, Timestamp toDate, Pageable pageable);

  List<AdmissionTests> findAllByOrgSlugAndRefererAndLocationOrderByCreatedAtDesc(
      String orgSlug, String referer, String location);

  @Query(
      """
    SELECT a FROM AdmissionTests a
    WHERE a.orgSlug = :orgSlug
      AND a.location = :location
      AND ((:referer IS NOT NULL AND a.referer = :referer)
         OR (:referer IS NULL AND (a.referer IS NULL OR a.referer = '')))  ORDER BY a.createdAt DESC""")
  List<AdmissionTests> getAdmissionsByOrgSlugLocationAndReferer(
      @Param("orgSlug") String orgSlug,
      @Param("location") String location,
      @Param("referer") String referer);

  @Query(
      value =
          """
          SELECT COALESCE(NULLIF(location, ''), 'Default') AS branch,
              DATE(created_at) AS testDate,
              COUNT(*) AS admissionCount
          FROM admission_tests
          WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
          AND org_slug = :orgSlug
          GROUP BY branch, testDate
          ORDER BY testDate DESC, branch""",
      nativeQuery = true)
  List<AdmissionTestMetricResult> getAWeekMetric(String orgSlug);

  @Query(
      value =
          """
          SELECT COALESCE(NULLIF(location, ''), 'Default') AS branch,
          COUNT(*) AS admissionCount
          FROM admission_tests
          WHERE org_slug =:orgSlug
          GROUP BY branch order by admissionCount desc limit :limit""",
      nativeQuery = true)
  List<AdmissionTestMetricResult> getBranchesMetric(String orgSlug, Integer limit);

  List<AdmissionTests> findAllByCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
      Timestamp fromDate, Timestamp toDate);

  @Query(
      value =
          """
          SELECT  COALESCE(NULLIF(referer, ''), 'No referer') AS admissionReferer,
          location as branch, COUNT(*) AS admissionCount
          FROM admission_tests
          WHERE org_slug = :orgSlug and location =:branch
          GROUP BY admissionReferer, branch
          order by  admissionCount desc""",
      nativeQuery = true)
  List<AdmissionTestMetricResult> getTestRefererMetric(String orgSlug, String branch);

  @Query(
      value =
          """
                          SELECT COALESCE(NULLIF(referer , ''), 'No referer') AS admissionReferer,
                            DATE(created_at) AS testDate,
                            COUNT(*) AS admissionCount
                            FROM admission_tests
                            WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
                            AND org_slug =:orgSlug and location =:branch
                            AND (referer IS NULL OR referer = '' OR referer IN ('Facebook', 'Google', 'Instagram', 'WhatsApp', 'Friend', 'Website', 'Newspaper', 'Banner', 'Flyer', 'Radio', 'TV'))
                            GROUP BY admissionReferer, DATE(created_at)
                            ORDER BY testDate DESC""",
      nativeQuery = true)
  List<AdmissionTestMetricResult> getRefererWeekMetric(String orgSlug, String branch);

  @Query(
      value =
          """
                  SELECT  COALESCE(NULLIF(referer, ''), 'No referer') AS admissionReferer,
                  "location"  as branch, COUNT(*) AS admissionCount
                  FROM admission_tests
                  WHERE org_slug = :orgSlug
                  GROUP BY admissionReferer, branch
                  order by  admissionCount desc  limit :limit""",
      nativeQuery = true)
  List<AdmissionTestMetricResult> getTopAdmissionsByReferer(String orgSlug, Integer limit);

  List<AdmissionTests> findAllByRefererAndCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
      String referer, Timestamp fromDate, Timestamp toDate);
}
